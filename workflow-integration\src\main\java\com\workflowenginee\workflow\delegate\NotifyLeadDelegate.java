
package com.workflowenginee.workflow.delegate;

import java.util.Map;

import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.stereotype.Component;

import com.workflowenginee.workflow.api.CompanyClient;
import com.workflowenginee.workflow.dto.NotifyToClientDto;
import com.workflowenginee.workflow.dto.NotifyUsersByRoleDto;
import com.workflowenginee.workflow.service.NotificationService;
import com.workflowenginee.workflow.util.ApiResponse;
import com.workflowenginee.workflow.util.Enums;
import com.workflowenginee.workflow.api.WorkplaceLearningClient;

@Component("notifyLeadDelegate")
public class NotifyLeadDelegate implements JavaDelegate {

    private final CompanyClient companyClient;
    private final NotificationService notificationService;
    private final WorkplaceLearningClient workplaceLearningClient;

    public NotifyLeadDelegate(CompanyClient companyClient, NotificationService notificationService, 
                            WorkplaceLearningClient workplaceLearningClient) {
        this.companyClient = companyClient;
        this.notificationService = notificationService;
        this.workplaceLearningClient = workplaceLearningClient;
    }

    @Override
    public void execute(DelegateExecution execution) {
        String processInstanceId = execution.getProcessInstanceId();
        String role = (String) execution.getVariable("role");
        String companyId = (String) execution.getVariable("companyId");
        String applicationType = (String) execution.getVariable("applicationType");
        String applicationStatus = (String) execution.getVariable("applicationStatus");
        String applicationState = (String) execution.getVariable("applicationState");
        String notifyToassignedBackOfficer = (String) execution.getVariable("notifyToassignedBackOfficer");
        System.out.println("application status : " + applicationStatus);
        System.out.println("applicationState :::: "+ applicationState);

        System.out.println("[Process: " + processInstanceId + "] Starting email notification process");
        System.out.println("companyId: " + companyId);

        Map<String, Object> applicationData;
        try {
            applicationData = (Map<String, Object>) execution.getVariable("ApplicationData");
        } catch (ClassCastException e) {
            System.err.println(
                    "[Process: " + processInstanceId + "] Error casting ApplicationData to Map: " + e.getMessage());
            execution.setVariable("emailSent", false);
            execution.setVariable("emailError", "Invalid application data format");
            return;
        }

        if (applicationData == null || !applicationData.containsKey("application")) {
            System.err.println("[Process: " + processInstanceId + "] No application data found to send email.");
            execution.setVariable("emailSent", false);
            execution.setVariable("emailError", "No application data found");
            return;
        }

        Map<String, Object> application;
        try {
            application = (Map<String, Object>) applicationData.get("application");
        } catch (ClassCastException e) {
            System.err.println(
                    "[Process: " + processInstanceId + "] Error casting 'application' object: " + e.getMessage());
            execution.setVariable("emailSent", false);
            execution.setVariable("emailError", "Invalid application structure");
            return;
        }

        try {
            String applicationId = String.valueOf(application.get("id"));
            String referenceNumber = String.valueOf(application.get("referenceNumber"));


            System.out
                    .println("[Process: " + processInstanceId + "] Preparing email for application: " + applicationId);
            System.out.println("[Process: " + processInstanceId + "] Reference Number: " + referenceNumber);
            System.out.println("[Process: " + processInstanceId + "] Application Status: " + applicationStatus);

            String emailSubject = "NCBSC Application " + referenceNumber + " - Status: " + applicationStatus;
            String emailBody = "Application ID: " + applicationId + "\n"
                    + "Reference Number: " + referenceNumber + "\n"
                    + "Status: " + applicationStatus + "\n\n"
                    + "This is an automated notification about the application's status update.";

            System.out.println("[Process: " + processInstanceId + "] Email Subject: " + emailSubject);
            System.out.println("[Process: " + processInstanceId + "] Email Body:\n" + emailBody);

            // Store in process variables
            execution.setVariable("emailSubject", emailSubject);
            execution.setVariable("emailBody", emailBody);
            execution.setVariable("emailSent", true);

            String subject = null;
            String message = null;

            if (applicationState != null && applicationStatus != null) {
            if (Enums.State.IN_PROCESSING.name().equalsIgnoreCase(applicationState) &&
                    Enums.Status.PENDING.name().equalsIgnoreCase(applicationStatus)) {
                subject = "Application Assigned to Agent";
                message = "Application " + referenceNumber + " has been assigned to you for processing";
            } else if (Enums.State.IN_REVIEW.name().equalsIgnoreCase(applicationState) &&
                    Enums.Status.PENDING.name().equalsIgnoreCase(applicationStatus)) {
                subject = "Application Under Review";
                message = "Your application " + referenceNumber + " is currently under review by the officer";
            } else if (Enums.State.IN_APPROVAL.name().equalsIgnoreCase(applicationState) &&
                    Enums.Status.PENDING.name().equalsIgnoreCase(applicationStatus)) {
                subject = "Application Approved by Officer";
                message = "Your application " + referenceNumber + " has been approved by the officer and is awaiting final approval";
            } else if (Enums.Status.APPROVED.name().equalsIgnoreCase(applicationStatus)) {
                subject = "Application Approved";
                message = "Your application " + referenceNumber + " has been approved by the manager";
            } else if (Enums.Status.REJECTED.name().equalsIgnoreCase(applicationStatus)) {
                subject = "Application Rejected";
                message = "We regret to inform you that your application " + referenceNumber + " has been rejected";
            } else if (Enums.Status.CHANGE_REQUEST.name().equalsIgnoreCase(applicationStatus)) {
                subject = "Additional Information Required";
                message = "Your application " + referenceNumber + " requires additional information. Please review and update accordingly";
            }
        }

            // Notification logic
            String currentActivityId = execution.getCurrentActivityId();

            if (Enums.Role.AGENT.name().equalsIgnoreCase(role)) {
                NotifyUsersByRoleDto notifyUsersByRoleDto = NotifyUsersByRoleDto.builder()
                        .role(Enums.Role.OFFICER_LEAD.name())
                        .referenceNumber(referenceNumber)
                        .applicationId(applicationId)
                        .applicationStatus(applicationStatus)
                        .applicationType(applicationType)
                        .actionBy(Enums.Role.AGENT.name())
                        .build();

                NotifyToClientDto notifyclientDto = NotifyToClientDto.builder()
                    .referenceNumber(referenceNumber)
                    .applicationId(applicationId)
                    .applicationStatus(applicationStatus)
                    .companyId(companyId)
                    .applicationState(applicationState)
                    .applicationType(applicationType)
                    .actionBy(Enums.Role.AGENT.name())
                    .build();

                notificationService.notifyUsersByRole(notifyUsersByRoleDto);
                notificationService.notifyToClient(notifyclientDto);
                notificationService.notifySpecificUser(notifyToassignedBackOfficer, referenceNumber, applicationId, subject, message, applicationType, applicationState, applicationStatus, Enums.Role.AGENT.name(),false);


            } else if (Enums.Role.AGENT_LEAD.name().equalsIgnoreCase(role)) {
                NotifyUsersByRoleDto notifyUsersByRoleDto = NotifyUsersByRoleDto.builder()
                        .role(Enums.Role.AGENT_LEAD.name())
                        .referenceNumber(referenceNumber)
                        .applicationId(applicationId)
                        .applicationStatus(applicationStatus)
                        .applicationType(applicationType)
                        .actionBy(Enums.Role.AGENT_LEAD.name())
                        .build();
                NotifyToClientDto notifyclientDto = NotifyToClientDto.builder()
                    .referenceNumber(referenceNumber)
                    .applicationId(applicationId)
                    .applicationStatus(applicationStatus)
                    .companyId(companyId)
                    .applicationState(applicationState)
                    .applicationType(applicationType)
                    .actionBy(Enums.Role.AGENT_LEAD.name())
                    .build();

                notificationService.notifyUsersByRole(notifyUsersByRoleDto);
                notificationService.notifyToClient(notifyclientDto);
                notificationService.notifySpecificUser(notifyToassignedBackOfficer, referenceNumber, applicationId, subject, message, applicationType, applicationState, applicationStatus, Enums.Role.AGENT_LEAD.name(),false);

            } else if (Enums.Role.OFFICER.name().equalsIgnoreCase(role)
                    || "serviceTask_Notifymanager".equalsIgnoreCase(currentActivityId)) {


                System.out.println("[Process: " + processInstanceId + "] Officer approved - notifying manager");
                NotifyUsersByRoleDto notifyUsersByRoleDto = NotifyUsersByRoleDto.builder()
                        .role(Enums.Role.MANAGER.name())
                        .referenceNumber(referenceNumber)
                        .applicationId(applicationId)
                        .applicationStatus(applicationStatus)
                        .applicationType(applicationType)
                        .actionBy(Enums.Role.OFFICER.name())
                        .build();
                notificationService.notifyUsersByRole(notifyUsersByRoleDto);

                NotifyToClientDto notifyclient = NotifyToClientDto.builder()
                        .referenceNumber(referenceNumber)
                        .applicationId(applicationId)
                        .applicationStatus(applicationStatus)
                        .companyId(companyId)
                        .applicationState(applicationState)
                        .applicationType(applicationType)
                        .actionBy(Enums.Role.OFFICER.name())
                        .build();
                notificationService.notifyToClient(notifyclient);
                notificationService.notifySpecificUser(notifyToassignedBackOfficer, referenceNumber, applicationId, subject, message, applicationType, applicationState, applicationStatus, Enums.Role.OFFICER.name(),false);


            } else if (Enums.Role.MANAGER.name().equalsIgnoreCase(role)) {
                 NotifyToClientDto notifyClientDto = NotifyToClientDto.builder()
                    .referenceNumber(referenceNumber)
                    .applicationId(applicationId)
                    .applicationStatus(applicationStatus)
                    .companyId(companyId)
                    .applicationState(applicationState)
                    .applicationType(applicationType)
                    .actionBy(Enums.Role.MANAGER.name())
                    .build();
                System.out.println("[Process: " + processInstanceId + "] Manager approved - notifying client");
                
                // Update manager approval date for RECOGNITION applications if approved
                if (Enums.ApplicationType.RECOGNITION.name().equalsIgnoreCase(applicationType) 
                    && Enums.Status.APPROVED.name().equalsIgnoreCase(applicationStatus)) {
                    updateManagerApprovalDate(processInstanceId, referenceNumber);
                }
                
                notificationService.notifyToClient(notifyClientDto);
            }

            System.out.println("[Process: " + processInstanceId + "] Notification flow completed.");

        } catch (Exception e) {
            System.err.println("[Process: " + processInstanceId + "] Error during email or notification processing: "
                    + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Updates the manager approval date for NCBSC applications using Feign client
     * @param processInstanceId the process instance ID for logging
     * @param referenceNumber the reference number of the application
     */
    private void updateManagerApprovalDate(String processInstanceId, String referenceNumber) {
        try {
            ApiResponse<String> response = workplaceLearningClient.updateManagerApprovalDate(referenceNumber);
            if (response.isStatus()) {
                System.out.println("[Process: " + processInstanceId + "] Manager approval date updated successfully for application: " + referenceNumber);
            } else {
                System.err.println("[Process: " + processInstanceId + "] Failed to update manager approval date for application " + referenceNumber + ": " + response.getMessage());
            }
        } catch (Exception e) {
            System.err.println("[Process: " + processInstanceId + "] Error updating manager approval date for application " + referenceNumber + ": " + e.getMessage());
            e.printStackTrace();
        }
    }

}
