2025-07-16T10:51:06.503+05:30  INFO 26904 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 26904 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-07-16T10:51:06.521+05:30  INFO 26904 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-07-16T10:51:06.654+05:30  INFO 26904 --- [workplace-learning] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-16T10:51:06.655+05:30  INFO 26904 --- [workplace-learning] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-16T10:51:12.284+05:30  INFO 26904 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-16T10:51:13.079+05:30  INFO 26904 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 775 ms. Found 31 JPA repository interfaces.
2025-07-16T10:51:14.261+05:30  INFO 26904 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=7f274aee-557c-39cf-a753-c8aa0338de55
2025-07-16T10:51:16.573+05:30  WARN 26904 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-16T10:51:16.632+05:30  WARN 26904 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-16T10:51:16.653+05:30  WARN 26904 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-16T10:51:16.673+05:30  WARN 26904 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-16T10:51:16.801+05:30  WARN 26904 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-16T10:51:19.903+05:30  INFO 26904 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-07-16T10:51:19.971+05:30  INFO 26904 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-16T10:51:19.991+05:30  INFO 26904 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-07-16T10:51:20.319+05:30  INFO 26904 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-16T10:51:20.323+05:30  INFO 26904 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 13666 ms
2025-07-16T10:51:21.269+05:30  INFO 26904 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-07-16T10:51:22.130+05:30  INFO 26904 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@73adcfbc
2025-07-16T10:51:22.160+05:30  INFO 26904 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-07-16T10:51:22.220+05:30  INFO 26904 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-07-16T10:51:23.817+05:30  INFO 26904 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-16T10:51:24.045+05:30  INFO 26904 --- [workplace-learning] [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.5.3.Final
2025-07-16T10:51:24.174+05:30  INFO 26904 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-16T10:51:25.375+05:30  INFO 26904 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-16T10:51:31.016+05:30  INFO 26904 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-16T10:51:31.848+05:30  INFO 26904 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-16T10:51:32.559+05:30  INFO 26904 --- [workplace-learning] [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-16T10:51:38.050+05:30  WARN 26904 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-16T10:51:38.317+05:30  WARN 26904 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-07-16T10:51:38.610+05:30  INFO 26904 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-07-16T10:51:40.812+05:30  INFO 26904 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-07-16T10:51:45.370+05:30  INFO 26904 --- [workplace-learning] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-07-16T10:51:47.972+05:30  INFO 26904 --- [workplace-learning] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-07-16T10:51:48.107+05:30  WARN 26904 --- [workplace-learning] [restartedMain] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-16T10:51:48.285+05:30  INFO 26904 --- [workplace-learning] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-16T10:51:48.320+05:30  INFO 26904 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-07-16T10:51:48.326+05:30  INFO 26904 --- [workplace-learning] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-16T10:51:48.338+05:30  INFO 26904 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-16T10:51:48.339+05:30  INFO 26904 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-16T10:51:48.339+05:30  INFO 26904 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-16T10:51:48.340+05:30  INFO 26904 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-16T10:51:48.340+05:30  INFO 26904 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-16T10:51:48.341+05:30  INFO 26904 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-16T10:51:48.341+05:30  INFO 26904 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-16T10:51:48.887+05:30  INFO 26904 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-16T10:51:48.891+05:30  INFO 26904 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-07-16T10:51:48.894+05:30  INFO 26904 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-16T10:51:48.898+05:30  INFO 26904 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1752643308896 with initial instances count: 5
2025-07-16T10:51:48.916+05:30  INFO 26904 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752643308916, current=UP, previous=STARTING]
2025-07-16T10:51:48.916+05:30  INFO 26904 --- [workplace-learning] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-07-16T10:51:48.917+05:30  INFO 26904 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-07-16T10:51:48.918+05:30  WARN 26904 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-07-16T10:51:48.939+05:30  INFO 26904 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8091 (http) with context path '/'
2025-07-16T10:51:48.941+05:30  INFO 26904 --- [workplace-learning] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8091
2025-07-16T10:51:48.990+05:30  INFO 26904 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-07-16T10:51:48.993+05:30  INFO 26904 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Started WorkplaceLearningApplication in 43.548 seconds (process running for 44.783)
2025-07-16T10:52:12.918+05:30  INFO 26904 --- [workplace-learning] [http-nio-8091-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-16T10:52:12.919+05:30  INFO 26904 --- [workplace-learning] [http-nio-8091-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-16T10:52:12.925+05:30  INFO 26904 --- [workplace-learning] [http-nio-8091-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 5 ms
2025-07-16T10:52:13.092+05:30  INFO 26904 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.c.NOCApplicationController     : NOC Application creation initiated
2025-07-16T10:52:13.093+05:30  INFO 26904 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.c.NOCApplicationController     : Checking ongoing application for recognition id REF-20250715-FB0C57
2025-07-16T10:52:13.465+05:30  INFO 26904 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.c.NOCApplicationController     : New NOC application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 created successfully
2025-07-16T10:53:16.424+05:30  INFO 26904 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.c.NOCApplicationController     : NOC Application quote accept status initiated
2025-07-16T10:53:16.439+05:30  INFO 26904 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.c.NOCApplicationController     : Application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 updated successfully, reference number generated and application submitted
2025-07-16T10:53:16.458+05:30  INFO 26904 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.c.NOCApplicationController     : Quotation for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 and application reference number c7fb3533-dcb4-445f-bad3-202fcd12fcb5 has been updated successfully
