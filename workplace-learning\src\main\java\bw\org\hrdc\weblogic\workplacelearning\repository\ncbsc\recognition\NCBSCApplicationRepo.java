package bw.org.hrdc.weblogic.workplacelearning.repository.ncbsc.recognition;

import bw.org.hrdc.weblogic.workplacelearning.dto.ncbsc.INCBSCApplicationQueryListDto;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.NCBSCApplication;
import lombok.NonNull;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.*;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import bw.org.hrdc.weblogic.workplacelearning.util.Enums;

/**
 * Repository for NonCreditBearingShortCourseApplication entity.
 */
@Repository
public interface NCBSCApplicationRepo extends JpaRepository<NCBSCApplication, UUID>, JpaSpecificationExecutor<NCBSCApplication> {
    @Query(value = "SELECT a.reference_number, a.organisation_id, a.short_course_delivery_mode, sci.title, a.date_submitted, " +
            "a.application_status, a.assigned_agent, a.assigned_agent_lead, a.assigned_officer_lead, a.assigned_officer, " +
            "a.assigned_manager FROM ncbsc_application a INNER JOIN short_course_information sci ON sci.application_id = a.uuid",
            nativeQuery = true
    )
    Page<INCBSCApplicationQueryListDto> findAllApplications(@NonNull Pageable pageable);

    @Query("SELECT a FROM NCBSCApplication a WHERE a.uuid = :id")
    @NonNull
    Optional<NCBSCApplication> findById(String id);

    @Query("SELECT a FROM NCBSCApplication a WHERE a.uuid = :uuid")
    Optional<NCBSCApplication> findByUuid(@Param("uuid") String uuid);

    @Modifying
    @Transactional
    @Query("UPDATE NCBSCApplication a SET a.isDeleted = true WHERE LOWER(a.uuid) = LOWER(:id)")
    int deleteById(@Param("id") String id);

    @Query("SELECT a FROM NCBSCApplication a WHERE LOWER(a.referenceNumber) = LOWER(:referenceNumber)")
    @NonNull
    Optional<NCBSCApplication> findByReferenceNumber(@Param("referenceNumber") String referenceNumber);

    @Modifying
    @Transactional
    @Query("UPDATE NCBSCApplication a SET " +
            "a.updatedAt = CURRENT_TIMESTAMP, " +
            "a.assignedAgent = CASE WHEN :role = 'AGENT' THEN :userId ELSE a.assignedAgent END, " +
            "a.assignedAgentLead = CASE WHEN :role = 'AGENT_LEAD' THEN :userId ELSE a.assignedAgentLead END, " +
            "a.assignedOfficer = CASE WHEN :role = 'OFFICER' THEN :userId ELSE a.assignedOfficer END, " +
            "a.assignedOfficerLead = CASE WHEN :role = 'OFFICER_LEAD' THEN :userId ELSE a.assignedOfficerLead END, " +
            "a.assignedManager = CASE WHEN :role = 'MANAGER' THEN :userId ELSE a.assignedManager END, " +
            "a.applicationState = " +
            "   CASE " +
            "       WHEN :role = 'AGENT' THEN 'IN_PROCESSING' " +
            "       WHEN :role = 'AGENT_LEAD' THEN 'SUBMITTED' " +
            "       WHEN :role = 'OFFICER' THEN 'IN_REVIEW' " +
            "       WHEN :role = 'OFFICER_LEAD' THEN 'IN_REVIEW' " +
            "       WHEN :role = 'MANAGER' THEN 'IN_APPROVAL' " +
            "       ELSE a.applicationState " +
            "   END, " +
            "a.applicationStatus = 'PENDING' " +
            "WHERE LOWER(a.referenceNumber) = LOWER(:referenceNumber)")
    int updateApplicationAssignedUser(@Param("referenceNumber") String referenceNumber, @Param("role") String role, @Param("userId") String userId);

    @Modifying
    @Query("UPDATE NCBSCApplication a " +
            "SET a.applicationState = CASE " +
            "    WHEN :role = 'AGENT' AND :action = 'APPROVED' THEN 'IN_REVIEW' " +
            "    WHEN :role = 'OFFICER' AND :action = 'APPROVED' THEN 'IN_APPROVAL' " +
            "    ELSE a.applicationState " +
            "END, " +
            "a.applicationStatus = CASE " +
            "    WHEN :role = 'AGENT' AND :action = 'APPROVED' THEN 'PENDING' " +
            "    WHEN :role = 'OFFICER' AND :action = 'APPROVED' THEN 'PENDING' " +
            "    ELSE :action " +
            "END " +
            "WHERE LOWER(a.referenceNumber) = LOWER(:referenceNumber)")
    int changeApplicationStatus(
            @Param("referenceNumber") String referenceNumber,
            @Param("role") String role,
            @Param("action") String action);

    @Query("SELECT a FROM NCBSCApplication a WHERE a.organisationId = :companyId")
    @NonNull
    Page<NCBSCApplication> findByCompanyId(@Param("companyId") String companyId, @NonNull Pageable pageable);

    @Query("SELECT a FROM NCBSCApplication a WHERE LOWER(a.applicationNumber) = LOWER(:applicationNumber)")
    @NonNull
    Optional<NCBSCApplication> findByApplicationNumber(@Param("applicationNumber") String applicationNumber);

    @Query("SELECT a FROM NCBSCApplication a " +
            "JOIN a.shortCourseInformation c " +
            "WHERE c.uuid = :courseId")
    @NonNull
    Optional<NCBSCApplication> findByCourseId(@Param("courseId") String courseId);

    @Modifying
    @Query("UPDATE NCBSCApplication a " +
            "SET a.referenceNumber = :referenceNumber, " +
            "a.applicationState = :applicationState, " +
            "a.applicationStatus = :applicationStatus " +
            "WHERE LOWER(a.uuid) = LOWER(:uuid)")
    int upgradeApplication(
            @Param("uuid") String uuid,
            @Param("referenceNumber") String referenceNumber,
            @Param("applicationState") String applicationState,
            @Param("applicationStatus") String applicationStatus);


    /**
     * Finds applications that are approaching their 3-year expiry (6 months before expiry) 
     * and have NOT yet been sent a renewal notification
     * @param startRange the start of the date range (3 years ago)
     * @param endRange the end of the date range (2.5 years ago)
     * @param status the application status (APPROVED)
     * @return List of applications that need renewal notification and haven't been notified yet
     */
    @Query("SELECT a FROM NCBSCApplication a WHERE a.managerApprovedAt BETWEEN :startRange AND :endRange " +
           "AND a.applicationStatus = :status " +
           "AND (a.renewalNotificationSent = false OR a.renewalNotificationSent IS NULL)")
    List<NCBSCApplication> findApplicationsNeedingRenewalNotificationNotYetSent(
            @Param("startRange") LocalDateTime startRange,
            @Param("endRange") LocalDateTime endRange,
            @Param("status") Enums.Status status);

    // Simple native SQL update to avoid circular reference issues
    @Modifying
    @Query(value = "UPDATE ncbsc_application SET renewal_notification_sent = true, renewal_notification_sent_at = CURRENT_TIMESTAMP WHERE uuid = :uuid", nativeQuery = true)
    void markRenewalNotificationAsSent(@Param("uuid") String uuid);

    @Query("SELECT COUNT(a) > 0 FROM NCBSCApplication a " +
            "WHERE a.organisationId = :organisationId " +
            "AND a.applicationStatus NOT IN (:excludedStatuses)")
    boolean existsByOrganisationIdAndApplicationStatusNotIn(
            @Param("organisationId") String organisationId,
            @Param("excludedStatuses") List<Enums.Status> excludedStatuses
    );

    // Workload counting methods for auto-assignment
    @Query(value = """
        SELECT assigned_agent, COUNT(*) AS application_count
        FROM ncbsc_application
        WHERE application_state = 'IN_PROCESSING'
        AND application_status = 'PENDING'
        AND deleted = false
        AND assigned_agent IS NOT NULL
        GROUP BY assigned_agent
        ORDER BY application_count ASC
        """, nativeQuery = true)
    List<Object[]> getPendingApplicationsByAgent();

    @Query(value = """
        SELECT assigned_officer, COUNT(*) AS application_count
        FROM ncbsc_application
        WHERE application_state = 'IN_REVIEW'
        AND application_status = 'PENDING'
        AND deleted = false
        AND assigned_officer IS NOT NULL
        GROUP BY assigned_officer
        ORDER BY application_count ASC
        """, nativeQuery = true)
    List<Object[]> getInReviewApplicationsByOfficer();

    @Query(value = """
        SELECT assigned_manager, COUNT(*) AS application_count
        FROM ncbsc_application
        WHERE application_state = 'IN_APPROVAL'
        AND application_status = 'PENDING'
        AND deleted = false
        AND assigned_manager IS NOT NULL
        GROUP BY assigned_manager
        ORDER BY application_count ASC
        """, nativeQuery = true)
    List<Object[]> getInApprovalApplicationsByManager();
}
