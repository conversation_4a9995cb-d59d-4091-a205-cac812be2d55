package com.workflowenginee.workflow.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.workflowenginee.workflow.util.Enums;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * Service for sending workflow email notifications to the existing sms-email-notification topic
 * This service creates EmailRequest compatible messages for the existing email infrastructure
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WorkflowEmailService {

    private final KafkaTemplate<String, String> kafkaTemplate;
    private final ObjectMapper objectMapper;

    private static final String EMAIL_TOPIC = "sms-email-notification";


    public void sendEmail(String userEmail, String userName, String subject, String message, 
                          String applicationType, String applicationId){

                
    }

    /**
     * Send workflow email notification using the existing email topic and format
     */
    public void sendWorkflowEmail(String userEmail, String userName, String subject, String message, 
                                String applicationType, String applicationId,String applicationStatus, String approvedStatus,String actionBy) {
        if (userEmail == null || userEmail.trim().isEmpty()) {
            log.warn("Cannot send email notification: email address is empty");
            return;
        }

        try {
            // Create email request in the exact format expected by existing EmailService
            Map<String, Object> emailRequest = new HashMap<>();
            
            // Standard EmailRequest fields (matching existing structure)
            emailRequest.put("contactAddress", userEmail);
            emailRequest.put("name", userName != null ? userName : "User");
            emailRequest.put("subject", subject);
            emailRequest.put("body", formatWorkflowMessage(message, applicationType, applicationId));
            emailRequest.put("communicationType", "EMAIL");
            emailRequest.put("applicationStatus", approvedStatus);
            
            // Optional fields that won't break existing functionality
            emailRequest.put("userId", userEmail);
            emailRequest.put("toMail", userEmail);
            emailRequest.put("applicationNumber", applicationId);
            emailRequest.put("applicationType", applicationType);
            emailRequest.put("actionBy", actionBy);

            System.out.println("actionBy : "+ actionBy);

            // Convert to JSON
            String emailJson = objectMapper.writeValueAsString(emailRequest);

            // Send to existing email topic
            kafkaTemplate.send(EMAIL_TOPIC, emailJson)
                .whenComplete((result, ex) -> {
                    if (ex == null) {
                        try {
                            log.info("Workflow email sent to [{}] for application [{}] - topic: {}, offset: {}, partition: {}",
                                    userEmail, applicationId,
                                    result.getRecordMetadata().topic(),
                                    result.getRecordMetadata().offset(),
                                    result.getRecordMetadata().partition());
                        } catch (Exception metaEx) {
                            log.error("Error reading Kafka metadata for email to {}: {}", 
                                    userEmail, metaEx.getMessage(), metaEx);
                        }
                    } else {
                        log.error("Failed to send workflow email to [{}]: {}", userEmail, ex.getMessage(), ex);
                    }
                });

        } catch (Exception e) {
            log.error("Exception while sending workflow email to [{}]: {}", userEmail, e.getMessage(), e);
        }
    }

    /**
     * Send pre-approval email notification
     */
    public void sendApplicationEmail(String userEmail, String userName, String applicationId, String applicationType,String applicationStatus, String applicationState,String actionBy) {
        System.out.println("applicationType "+ applicationType);
        Map<String, String> subjectResult = generateSubject(applicationType,applicationStatus, applicationState);
        String subject = subjectResult.get("subject");
        String approvedStatus = subjectResult.get("approvedStatus");
        String message = generateMessage(applicationType, applicationStatus, applicationState);
        sendWorkflowEmail(userEmail, userName, subject, message, applicationType, applicationId, applicationStatus, approvedStatus,actionBy);
    }

    
    public void sendApplicationEmailForNoc(String userEmail, String userName, String applicationId, String applicationType,String applicationStatus, String applicationState,String actionBy) {
        System.out.println("applicationType "+ applicationType);
        Map<String, String> subjectResult = generateSubjectNoc(applicationType,applicationStatus, applicationState);
        String subject = subjectResult.get("subject");
        String approvedStatus = subjectResult.get("approvedStatus");
        String message = generateMessageNoc(applicationType, applicationStatus, applicationState);
        sendWorkflowEmail(userEmail, userName, subject, message, applicationType, applicationId, applicationStatus, approvedStatus,actionBy);
    }

    /**
     * Format workflow message with additional context
     */
    private String formatWorkflowMessage(String message, String applicationType, String applicationId) {
        StringBuilder formattedMessage = new StringBuilder();
        
        formattedMessage.append(message);
        
        return formattedMessage.toString();
    }

    // Helper methods for generating subjects and messages
    private Map<String, String> generateSubject(String applicationType, String applicationStatus, String applicationState) {
        System.out.println("applicationType  : "+ applicationType);
        System.out.println("application status  : "+ applicationStatus);
        System.out.println("application state  : "+ applicationState);
        String subject = "";
        String approvedStatus = "";

        if (applicationState != null && applicationStatus != null) {
            if (Enums.State.IN_REVIEW.name().equalsIgnoreCase(applicationState) &&
                    Enums.Status.PENDING.name().equalsIgnoreCase(applicationStatus)) {
                subject = applicationType +" Approved by Agent";
                approvedStatus = "APPROVED";
            } else if (Enums.State.IN_PROCESSING.name().equals(applicationState) && Enums.Status.PENDING.name().equalsIgnoreCase(applicationStatus)) {
                System.out.println("$$$$$$$$$$$$$$$$$$$$$$$$");
                subject = applicationType +" Application Submitted";
                approvedStatus = "SUBMITTED";
            } else if (Enums.State.IN_APPROVAL.name().equalsIgnoreCase(applicationState) &&
                    Enums.Status.PENDING.name().equalsIgnoreCase(applicationStatus)) {
                subject = applicationType +" Application Approved by Officer";
                approvedStatus = "APPROVED";
            } else if (Enums.Status.APPROVED.name().equalsIgnoreCase(applicationStatus)) {
                subject = applicationType +" Application Approved";
                approvedStatus = "APPROVED";
            } else if (Enums.Status.REJECTED.name().equalsIgnoreCase(applicationStatus)) {
                subject = applicationType +" Application Rejected";
                approvedStatus = "REJECTED";
            } else if (Enums.Status.CHANGE_REQUEST.name().equalsIgnoreCase(applicationStatus)) {
                subject = applicationType+" Additional Information Required";
                approvedStatus = "CHANGE_REQUEST";
            } 
        }
        
        Map<String, String> result = new HashMap<>();
        result.put("subject", subject);
        result.put("approvedStatus", approvedStatus);
        return result;
    }

     private Map<String, String> generateSubjectNoc(String applicationType, String applicationStatus, String applicationState) {
        System.out.println("applicationType  : "+ applicationType);
        System.out.println("application status  : "+ applicationStatus);
        System.out.println("application state  : "+ applicationState);
        String subject = "";
        String approvedStatus = "";

        if (applicationState != null && applicationStatus != null) {
            if (Enums.State.SUBMITTED.name().equalsIgnoreCase(applicationState) &&
                    Enums.Status.PENDING_PAYMENT.name().equalsIgnoreCase(applicationStatus)) {
                subject = applicationType +" Approved by Submitted";
                approvedStatus = "SUBMITTED";
            } else if (Enums.State.IN_REVIEW.name().equals(applicationState) && Enums.Status.PENDING.name().equalsIgnoreCase(applicationStatus)) {
                subject = applicationType +" Application Approved by agent";
                approvedStatus = "APPROVED";
            } else if (Enums.State.IN_APPROVAL.name().equalsIgnoreCase(applicationState) &&
                    Enums.Status.PENDING.name().equalsIgnoreCase(applicationStatus)) {
                subject = applicationType +" Application Approved by Officer";
                approvedStatus = "APPROVED";
            } else if (Enums.Status.APPROVED.name().equalsIgnoreCase(applicationStatus)) {
                subject = applicationType +" Application Approved";
                approvedStatus = "APPROVED";
            } else if (Enums.Status.REJECTED.name().equalsIgnoreCase(applicationStatus)) {
                subject = applicationType +" Application Rejected";
                approvedStatus = "REJECTED";
            } else if (Enums.Status.CHANGE_REQUEST.name().equalsIgnoreCase(applicationStatus)) {
                subject = applicationType+" Additional Information Required";
                approvedStatus = "CHANGE_REQUEST";
            } 
        }
        
        Map<String, String> result = new HashMap<>();
        result.put("subject", subject);
        result.put("approvedStatus", approvedStatus);
        return result;
    }


    private String generateMessageNoc(String applicationType, String applicationStatus, String applicationState) {
        System.out.println("application state  :: "+ applicationState);
        System.out.println("application status  :: "+ applicationStatus);
        String message = "";

        if (applicationState != null && applicationStatus != null) {
            if (Enums.State.IN_REVIEW.name().equalsIgnoreCase(applicationState) &&
                    Enums.Status.PENDING.name().equalsIgnoreCase(applicationStatus)) {
                message = applicationType +" application has been approved by agent and is now under review.";
            } else if (Enums.State.IN_APPROVAL.name().equalsIgnoreCase(applicationState) && Enums.Status.APPROVED.name().equalsIgnoreCase(applicationStatus)) {
                message = applicationType+" application has been approved ";
            }
            else if (Enums.State.SUBMITTED.name().equals(applicationState) && Enums.Status.PENDING_PAYMENT.name().equalsIgnoreCase(applicationStatus)) {
                System.out.println("HHHHHHHHHHHHHHHHHHHHHHHH");
                message =applicationType +" application has been successfully submitted.";
            } else if (Enums.State.IN_APPROVAL.name().equalsIgnoreCase(applicationState) &&
                    Enums.Status.PENDING.name().equalsIgnoreCase(applicationStatus)) {
                message = applicationType +" application has been approved by officer and is now in final approval stage.";
            } else if (Enums.Status.APPROVED.name().equalsIgnoreCase(applicationStatus)) {
                message = applicationType+" application has been approved.";
            } else if (Enums.Status.REJECTED.name().equalsIgnoreCase(applicationStatus)) {
                message = applicationType+" application has been rejected.";
            } else if (Enums.Status.CHANGE_REQUEST.name().equalsIgnoreCase(applicationStatus)) {
                message = applicationType+" application requires additional information or modifications.";
            } 
        }
        return message;
    }

      private String generateMessage(String applicationType, String applicationStatus, String applicationState) {
        System.out.println("application state  :: "+ applicationState);
        System.out.println("application status  :: "+ applicationStatus);
        String message = "";

        if (applicationState != null && applicationStatus != null) {
            if (Enums.State.IN_REVIEW.name().equalsIgnoreCase(applicationState) &&
                    Enums.Status.PENDING.name().equalsIgnoreCase(applicationStatus)) {
                message = applicationType +" application has been approved by agent and is now under review.";
            } else if (Enums.State.IN_APPROVAL.name().equalsIgnoreCase(applicationState) && Enums.Status.APPROVED.name().equalsIgnoreCase(applicationStatus)) {
                message = applicationType+" application has been approved ";
            }
            else if (Enums.State.IN_PROCESSING.name().equals(applicationState) && Enums.Status.PENDING.name().equalsIgnoreCase(applicationStatus)) {
                System.out.println("HHHHHHHHHHHHHHHHHHHHHHHH");
                message =applicationType +" application has been successfully submitted.";
            } else if (Enums.State.IN_APPROVAL.name().equalsIgnoreCase(applicationState) &&
                    Enums.Status.PENDING.name().equalsIgnoreCase(applicationStatus)) {
                message = applicationType +" application has been approved by officer and is now in final approval stage.";
            } else if (Enums.Status.APPROVED.name().equalsIgnoreCase(applicationStatus)) {
                message = applicationType+" application has been approved.";
            } else if (Enums.Status.REJECTED.name().equalsIgnoreCase(applicationStatus)) {
                message = applicationType+" application has been rejected.";
            } else if (Enums.Status.CHANGE_REQUEST.name().equalsIgnoreCase(applicationStatus)) {
                message = applicationType+" application requires additional information or modifications.";
            } 
        }
        return message;
    }

}