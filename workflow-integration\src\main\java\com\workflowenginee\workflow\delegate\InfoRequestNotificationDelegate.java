package com.workflowenginee.workflow.delegate;

import java.util.Map;

import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.stereotype.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.workflowenginee.workflow.dto.NotifyToClientDto;
import com.workflowenginee.workflow.service.NotificationService;
import com.workflowenginee.workflow.util.Enums;

@Component("infoRequestNotificationDelegate")
public class InfoRequestNotificationDelegate implements JavaDelegate {

    private static final Logger logger = LoggerFactory.getLogger(InfoRequestNotificationDelegate.class);
    
    private final NotificationService notificationService;
    
    public InfoRequestNotificationDelegate(NotificationService notificationService) {
        this.notificationService = notificationService;
    }

    @Override
    public void execute(DelegateExecution execution) {
        String processInstanceId = execution.getProcessInstanceId();
        Map<String, Object> applicationData = null;

        try {
            applicationData = (Map<String, Object>) execution.getVariable("ApplicationData");
        } catch (ClassCastException e) {
            logger.error("[Process: {}] Error casting ApplicationData to Map: {}", processInstanceId, e.getMessage(), e);
            execution.setVariable("infoRequestNotificationSent", false);
            execution.setVariable("infoRequestNotificationError", "Invalid application data format");
            return;
        }

        String companyId = (String) execution.getVariable("companyId");

        logger.info("[Process: {}] Handling information request notification", processInstanceId);

        if (applicationData == null || !applicationData.containsKey("application")) {
            logger.error("[Process: {}] No application data found to send info request notification", processInstanceId);
            execution.setVariable("infoRequestNotificationSent", false);
            execution.setVariable("infoRequestNotificationError", "No application data found");
            return;
        }

        Map<String, Object> application;
        try {
            application = (Map<String, Object>) applicationData.get("application");
        } catch (ClassCastException e) {
            logger.error("[Process: {}] Error casting 'application' data: {}", processInstanceId, e.getMessage(), e);
            execution.setVariable("infoRequestNotificationSent", false);
            execution.setVariable("infoRequestNotificationError", "Invalid application structure");
            return;
        }

        try {
            // Extract required values
            String referenceNumber = String.valueOf(application.get("referenceNumber"));
           
            String assignedOfficerName = (String) execution.getVariable("assignedOfficerName");
            String applicationId = (String) execution.getVariable("applicationId");
            String applicationType = (String) execution.getVariable("applicationType");
            String actionBy = (String) execution.getVariable("role");
            System.out.println("Action By : " +actionBy);
            String applicationStatus = "";

            String applicationState = "";
            if (applicationType.equalsIgnoreCase(Enums.ApplicationType.PRE_APPROVAL.name())) {
                applicationState = String.valueOf(application.get("state"));
                applicationStatus = String.valueOf(application.get("status"));
            }
            else{
            applicationState = String.valueOf(application.get("applicationState"));
            applicationStatus = String.valueOf(application.get("applicationStatus"));

            }

            String infoRequestDetails = (String) execution.getVariable("infoRequestDetails");
            if (infoRequestDetails == null || infoRequestDetails.isBlank()) {
                infoRequestDetails = "Additional information is required to process your application";
            }

            logger.info("[Process: {}] Preparing to send info request for application ID: {}", processInstanceId, applicationId);
            logger.info("[Process: {}] Reference Number: {}", processInstanceId, referenceNumber);

            // Update process variables
            execution.setVariable("applicationStatus", Enums.Status.CHANGE_REQUEST.name());
            execution.setVariable("infoRequestDetails", infoRequestDetails);

            // Default to successful unless an error occurs
            execution.setVariable("infoRequestNotificationSent", true);

            String currentRole = (String) execution.getVariable("role");
            String activityId = execution.getCurrentActivityId();

            logger.info("[Process: {}] Current activity ID: {}, Role: {}", processInstanceId, activityId, currentRole);

            // Notify client
            try {

                if (Enums.ApplicationType.PRE_APPROVAL.name().equalsIgnoreCase(applicationType)|| Enums.ApplicationType.WORK_SKILLS.name().equalsIgnoreCase(applicationType)) {
                    
                       NotifyToClientDto notifyClientDto = NotifyToClientDto.builder()
                        .referenceNumber(applicationId)
                        .applicationId(applicationId)
                        .applicationStatus(Enums.Status.CHANGE_REQUEST.name())
                        .companyId(companyId)
                        .applicationState(applicationState)
                        .applicationType(applicationType)
                        .actionBy(actionBy)
                        .build();

                notificationService.notifyToClient(notifyClientDto);
                } else {


                       NotifyToClientDto notifyClientDto = NotifyToClientDto.builder()
                        .referenceNumber(referenceNumber)
                        .applicationId(applicationId)
                        .applicationStatus(Enums.Status.CHANGE_REQUEST.name())
                        .companyId(companyId)
                        .applicationState(applicationState)
                        .applicationType(applicationType)
                        .actionBy(actionBy)
                        .build();

                notificationService.notifyToClient(notifyClientDto);
                }
             
            } catch (Exception clientEx) {
                logger.error("[Process: {}] Failed to notify client: {}", processInstanceId, clientEx.getMessage(), clientEx);
                execution.setVariable("infoRequestNotificationSent", false);
                execution.setVariable("infoRequestNotificationError", "Failed to notify client");
                return;
            }

            // If manager, notify assigned officer too
            if (Enums.Role.MANAGER.name().equalsIgnoreCase(currentRole)) {
                if (assignedOfficerName != null && !assignedOfficerName.isBlank()) {
                    try {

                        if (Enums.ApplicationType.PRE_APPROVAL.name().equalsIgnoreCase(applicationType) || Enums.ApplicationType.WORK_SKILLS.name().equalsIgnoreCase(applicationType)) {
                            referenceNumber = applicationId; // Use applicationId for these types
                             notificationService.notifySpecificUser(
                                assignedOfficerName,
                                applicationId,
                                applicationId,
                                "Additional Information Required",
                                "The manager has requested additional information for application " + referenceNumber,
                                applicationType,
                                applicationState,
                                applicationStatus,
                                Enums.Role.MANAGER.name(), true
                            );
                        } else {

                             notificationService.notifySpecificUser(
                                assignedOfficerName,
                                referenceNumber,
                                applicationId,
                                "Additional Information Required",
                                "The manager has requested additional information for application " + referenceNumber,
                                applicationType,
                                applicationState,
                                applicationStatus,
                                Enums.Role.MANAGER.name(), true
                            );
                        }

                        logger.info("[Process: {}] Notified officer {} of manager's info request", processInstanceId, assignedOfficerName);
                    } catch (Exception officerEx) {
                        logger.warn("[Process: {}] Failed to notify assigned officer {}: {}", processInstanceId, assignedOfficerName, officerEx.getMessage(), officerEx);
                    }
                } else {
                    logger.warn("[Process: {}] No assigned officer found to notify", processInstanceId);
                }
            }

            logger.info("[Process: {}] Info request notification sent successfully", processInstanceId);

        } catch (Exception e) {
            logger.error("[Process: {}] Unexpected error sending info request notification: {}", processInstanceId, e.getMessage(), e);
            execution.setVariable("infoRequestNotificationSent", false);
            execution.setVariable("infoRequestNotificationError", "Unexpected error: " + e.getMessage());
        }
    }

}